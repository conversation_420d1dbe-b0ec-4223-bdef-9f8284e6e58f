using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Cache invalidation aspect - Belirtilen pattern'lere göre cache'i temizler
    /// Multi-tenant mimaride company bazlı cache temizleme desteği
    /// </summary>
    public class CacheRemoveAspect : MethodInterceptionBaseAttribute
    {
        private readonly string _pattern;
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;

        /// <summary>
        /// Cache remove aspect constructor
        /// </summary>
        /// <param name="pattern">Temizlenecek cache pattern'i. Örnek: "gym:{companyId}:member:*"</param>
        public CacheRemoveAspect(string pattern)
        {
            _pattern = pattern;
            Priority = 2; // Cache remove aspect'i cache aspect'ten sonra çalışsın
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
        }

        /// <summary>
        /// Method çalıştırıldıktan sonra cache'i temizler
        /// </summary>
        protected override void OnSuccess(IInvocation invocation)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                
                // Pattern'de {companyId} placeholder'ını gerçek company ID ile değiştir
                var resolvedPattern = _pattern.Replace("{companyId}", companyId.ToString());
                
                // Cache'i temizle
                _cacheService.RemoveByPattern(resolvedPattern);
                
                // Log cache removal
                LogCacheRemoval(invocation, resolvedPattern);
            }
            catch (Exception ex)
            {
                // Cache temizleme hatası business logic'i etkilememeli
                LogCacheError(invocation, ex);
            }
        }

        /// <summary>
        /// Cache temizleme işlemini loglar
        /// </summary>
        private void LogCacheRemoval(IInvocation invocation, string pattern)
        {
            var className = invocation.Method.ReflectedType?.Name ?? "Unknown";
            var methodName = invocation.Method.Name;
            
            // Performance ve debugging için log
            System.Diagnostics.Debug.WriteLine(
                $"[CacheRemove] {className}.{methodName} - Pattern: {pattern}"
            );
        }

        /// <summary>
        /// Cache temizleme hatalarını loglar
        /// </summary>
        private void LogCacheError(IInvocation invocation, Exception ex)
        {
            var className = invocation.Method.ReflectedType?.Name ?? "Unknown";
            var methodName = invocation.Method.Name;
            
            System.Diagnostics.Debug.WriteLine(
                $"[CacheRemove Error] {className}.{methodName} - Error: {ex.Message}"
            );
        }
    }
}
