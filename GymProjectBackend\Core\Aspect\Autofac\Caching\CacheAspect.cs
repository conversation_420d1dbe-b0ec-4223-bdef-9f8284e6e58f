using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// AOP Cache Aspect - Method'lara otomatik cache ekleme
    /// Multi-tenant aware, parameter-based key generation
    /// Performance monitoring ve cache hit/miss logging
    /// </summary>
    public class CacheAspect : MethodInterceptionBaseAttribute
    {
        private readonly int _duration;
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly Stopwatch _stopwatch;

        /// <summary>
        /// Cache aspect constructor
        /// </summary>
        /// <param name="duration">Cache süresi (saniye)</param>
        public CacheAspect(int duration)
        {
            _duration = duration;
            Priority = 1; // Cache aspect'i en önce çalışsın

            Console.WriteLine($"[CACHE DEBUG] CacheAspect constructor called with duration: {duration}");

            Console.WriteLine($"[CACHE DEBUG] ServiceTool.ServiceProvider: {(ServiceTool.ServiceProvider != null ? "Available" : "NULL")}");

            _cacheService = ServiceTool.ServiceProvider?.GetService<ICacheService>();
            Console.WriteLine($"[CACHE DEBUG] ICacheService: {(_cacheService != null ? "Available" : "NULL")}");

            _companyContext = ServiceTool.ServiceProvider?.GetService<ICompanyContext>();
            Console.WriteLine($"[CACHE DEBUG] ICompanyContext: {(_companyContext != null ? "Available" : "NULL")}");

            _stopwatch = new Stopwatch();
        }

        public override void Intercept(IInvocation invocation)
        {
            var methodName = $"{invocation.Method.ReflectedType.FullName}.{invocation.Method.Name}";
            Console.WriteLine($"[CACHE DEBUG] Intercepting method: {methodName}");

            // Cache service kontrolü
            if (_cacheService == null)
            {
                Console.WriteLine($"[CACHE DEBUG] Cache service is null, proceeding without cache");
                invocation.Proceed();
                return;
            }

            // Company context kontrolü
            if (_companyContext == null)
            {
                Console.WriteLine($"[CACHE DEBUG] Company context is null, proceeding without cache");
                invocation.Proceed();
                return;
            }

            var cacheKey = GenerateCacheKey(invocation);
            var returnType = invocation.Method.ReturnType;

            _stopwatch.Start();

            try
            {
                // Cache'den veri al - Return type'a göre generic method çağır
                var cachedValue = GetCachedValue(cacheKey, returnType);

                if (cachedValue != null)
                {
                    // Cache hit - Cached değeri döndür
                    invocation.ReturnValue = cachedValue;
                    _stopwatch.Stop();

                    LogCacheHit(methodName, cacheKey, _stopwatch.ElapsedMilliseconds);
                    return;
                }

                // Cache miss - Method'u çalıştır
                invocation.Proceed();

                // Return value'yu cache'le
                if (invocation.ReturnValue != null)
                {
                    var expiry = TimeSpan.FromSeconds(_duration);
                    SetCachedValue(cacheKey, invocation.ReturnValue, returnType, expiry);
                }

                _stopwatch.Stop();
                LogCacheMiss(methodName, cacheKey, _stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                _stopwatch.Stop();
                LogCacheError(methodName, cacheKey, ex);

                // Cache hatası durumunda method'u normal çalıştır
                Console.WriteLine($"[CACHE DEBUG] Cache error, proceeding without cache");
                invocation.Proceed();
            }
        }

        /// <summary>
        /// Method signature ve parametrelerine göre cache key oluşturur
        /// Format: "gym:{companyId}:{className}:{methodName}:{parameterHash}"
        /// </summary>
        private string GenerateCacheKey(IInvocation invocation)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                var className = invocation.Method.ReflectedType.Name.Replace("Manager", "").ToLowerInvariant();
                var methodName = invocation.Method.Name.ToLowerInvariant();

                Console.WriteLine($"[CACHE DEBUG] GenerateCacheKey - CompanyId: {companyId}, Class: {className}, Method: {methodName}");

                // Parameter hash oluştur
                var parameterHash = GenerateParameterHash(invocation);
                Console.WriteLine($"[CACHE DEBUG] Parameter Hash: {parameterHash}");

                // CacheKeyHelper kullanarak hierarchical key oluştur
                // Format: gym:{companyId}:{entity}:{action}:{additionalParts}
                var cacheKey = CacheKeyHelper.GenerateKey(companyId, className, methodName, parameterHash);
                Console.WriteLine($"[CACHE DEBUG] Generated Cache Key: {cacheKey}");

                return cacheKey;
            }
            catch (Exception ex)
            {
                // Fallback key generation
                var fallbackKey = $"gym:{_companyContext.GetCompanyId()}:cache_error:{invocation.Method.Name}:{DateTime.UtcNow.Ticks}";
                Console.WriteLine($"[CACHE DEBUG] Fallback Key: {fallbackKey}");
                LogCacheError($"{invocation.Method.ReflectedType.FullName}.{invocation.Method.Name}",
                             fallbackKey, ex);
                return fallbackKey;
            }
        }

        /// <summary>
        /// Method parametrelerinden hash oluşturur
        /// </summary>
        private string GenerateParameterHash(IInvocation invocation)
        {
            if (invocation.Arguments == null || invocation.Arguments.Length == 0)
            {
                return "noparams";
            }

            var paramBuilder = new StringBuilder();
            var parameters = invocation.Method.GetParameters();

            for (int i = 0; i < invocation.Arguments.Length; i++)
            {
                var arg = invocation.Arguments[i];
                var paramName = parameters[i].Name;

                if (arg == null)
                {
                    paramBuilder.Append($"{paramName}:null");
                }
                else if (IsPrimitiveType(arg.GetType()))
                {
                    paramBuilder.Append($"{paramName}:{arg}");
                }
                else
                {
                    // Complex object için JSON hash
                    try
                    {
                        var json = JsonConvert.SerializeObject(arg, Formatting.None);
                        var hash = json.GetHashCode().ToString("x8");
                        paramBuilder.Append($"{paramName}:{hash}");
                    }
                    catch
                    {
                        paramBuilder.Append($"{paramName}:{arg.GetType().Name}");
                    }
                }

                if (i < invocation.Arguments.Length - 1)
                    paramBuilder.Append("_");
            }

            return paramBuilder.ToString().ToLowerInvariant();
        }

        /// <summary>
        /// Primitive type kontrolü
        /// </summary>
        private bool IsPrimitiveType(Type type)
        {
            return type.IsPrimitive || 
                   type == typeof(string) || 
                   type == typeof(DateTime) || 
                   type == typeof(decimal) || 
                   type == typeof(Guid) ||
                   type.IsEnum;
        }

        /// <summary>
        /// Cache hit logging
        /// </summary>
        private void LogCacheHit(string methodName, string cacheKey, long elapsedMs)
        {
            var logMessage = $"✅ CACHE HIT: {methodName} | Key: {cacheKey} | Duration: {elapsedMs}ms";
            Console.WriteLine($"[CACHE] {logMessage}");

            // TODO: ILogService ile structured logging eklenebilir
        }

        /// <summary>
        /// Cache miss logging
        /// </summary>
        private void LogCacheMiss(string methodName, string cacheKey, long elapsedMs)
        {
            var logMessage = $"❌ CACHE MISS: {methodName} | Key: {cacheKey} | Duration: {elapsedMs}ms | Cached for {_duration}s";
            Console.WriteLine($"[CACHE] {logMessage}");

            // TODO: ILogService ile structured logging eklenebilir
        }

        /// <summary>
        /// Cache error logging
        /// </summary>
        private void LogCacheError(string methodName, string cacheKey, Exception ex)
        {
            var logMessage = $"🚨 CACHE ERROR: {methodName} | Key: {cacheKey} | Error: {ex.Message}";
            Console.WriteLine($"[CACHE] {logMessage}");
            Console.WriteLine($"[CACHE] Stack Trace: {ex.StackTrace}");

            // TODO: ILogService ile structured logging eklenebilir
        }

        /// <summary>
        /// Type-safe cache value retrieval
        /// </summary>
        private object GetCachedValue(string cacheKey, Type returnType)
        {
            try
            {
                Console.WriteLine($"[CACHE DEBUG] GetCachedValue - Key: {cacheKey}, Type: {returnType.FullName}");

                if (_cacheService == null)
                {
                    Console.WriteLine($"[CACHE DEBUG] Cache service is null in GetCachedValue");
                    return null;
                }

                // Basit string get ile test edelim
                var stringValue = _cacheService.Get<string>(cacheKey);
                Console.WriteLine($"[CACHE DEBUG] String Value: {(stringValue != null ? $"Found ({stringValue.Length} chars)" : "Not Found")}");

                if (stringValue == null)
                    return null;

                if (string.IsNullOrWhiteSpace(stringValue))
                {
                    Console.WriteLine($"[CACHE DEBUG] String value is empty or whitespace");
                    return null;
                }

                // JSON'dan deserialize et - Interface type'lar için concrete type kullan
                var result = DeserializeWithConcreteType(stringValue, returnType);

                Console.WriteLine($"[CACHE DEBUG] GetCachedValue Result: {(result != null ? "Successfully deserialized" : "Deserialization failed")}");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CACHE DEBUG] GetCachedValue Error: {ex.Message}");
                Console.WriteLine($"[CACHE DEBUG] GetCachedValue Stack: {ex.StackTrace}");
                LogCacheError("GetCachedValue", cacheKey, ex);
                return null;
            }
        }

        /// <summary>
        /// Type-safe cache value storage
        /// </summary>
        private void SetCachedValue(string cacheKey, object value, Type valueType, TimeSpan expiry)
        {
            try
            {
                Console.WriteLine($"[CACHE DEBUG] SetCachedValue - Key: {cacheKey}, Type: {valueType.FullName}, Expiry: {expiry.TotalSeconds}s");

                // JSON serialize edip string olarak kaydet
                var jsonValue = JsonConvert.SerializeObject(value);
                _cacheService.Set(cacheKey, jsonValue, expiry);

                Console.WriteLine($"[CACHE DEBUG] SetCachedValue Success");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CACHE DEBUG] SetCachedValue Error: {ex.Message}");
                LogCacheError("SetCachedValue", cacheKey, ex);
            }
        }

        /// <summary>
        /// Interface type'lar için concrete type ile deserialization
        /// </summary>
        private object DeserializeWithConcreteType(string jsonValue, Type returnType)
        {
            try
            {
                // IDataResult<T> interface'i için SuccessDataResult<T> concrete type kullan
                if (returnType.IsGenericType && returnType.GetGenericTypeDefinition() == typeof(Core.Utilities.Results.IDataResult<>))
                {
                    var genericArg = returnType.GetGenericArguments()[0];
                    var concreteType = typeof(Core.Utilities.Results.SuccessDataResult<>).MakeGenericType(genericArg);

                    Console.WriteLine($"[CACHE DEBUG] Using concrete type: {concreteType.FullName}");
                    return JsonConvert.DeserializeObject(jsonValue, concreteType);
                }

                // Diğer interface'ler için varsayılan deserialization
                return JsonConvert.DeserializeObject(jsonValue, returnType);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CACHE DEBUG] DeserializeWithConcreteType Error: {ex.Message}");
                throw;
            }
        }
    }
}
